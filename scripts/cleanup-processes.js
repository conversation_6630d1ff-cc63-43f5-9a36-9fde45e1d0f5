#!/usr/bin/env node

/**
 * Cleanup script for CI processes
 * Gracefully terminates background processes started during CI
 */

const { exec } = require('child_process');
const util = require('util');
const execAsync = util.promisify(exec);

const PORTS_TO_CLEANUP = [3000, 3001];
const PROCESS_NAMES = ['node', 'react-scripts'];

async function findProcessesByPort(port) {
  try {
    const { stdout } = await execAsync(`lsof -ti:${port}`);
    return stdout.trim().split('\n').filter(pid => pid);
  } catch (error) {
    // No processes found on this port
    return [];
  }
}

async function findProcessesByName(name) {
  try {
    const { stdout } = await execAsync(`pgrep -f "${name}"`);
    return stdout.trim().split('\n').filter(pid => pid);
  } catch (error) {
    // No processes found with this name
    return [];
  }
}

async function killProcess(pid, signal = 'TERM') {
  try {
    await execAsync(`kill -${signal} ${pid}`);
    console.log(`✅ Killed process ${pid} with signal ${signal}`);
    return true;
  } catch (error) {
    console.log(`⚠️  Failed to kill process ${pid}: ${error.message}`);
    return false;
  }
}

async function waitForProcessToExit(pid, timeout = 5000) {
  const start = Date.now();
  while (Date.now() - start < timeout) {
    try {
      await execAsync(`kill -0 ${pid}`);
      await new Promise(resolve => setTimeout(resolve, 100));
    } catch (error) {
      // Process no longer exists
      return true;
    }
  }
  return false;
}

async function cleanup() {
  console.log('🧹 Starting process cleanup...');
  
  const allPids = new Set();
  
  // Find processes by port
  for (const port of PORTS_TO_CLEANUP) {
    console.log(`🔍 Checking port ${port}...`);
    const pids = await findProcessesByPort(port);
    pids.forEach(pid => allPids.add(pid));
  }
  
  // Find processes by name
  for (const name of PROCESS_NAMES) {
    console.log(`🔍 Checking processes named "${name}"...`);
    const pids = await findProcessesByName(name);
    pids.forEach(pid => allPids.add(pid));
  }
  
  if (allPids.size === 0) {
    console.log('✅ No processes to cleanup');
    return;
  }
  
  console.log(`📋 Found ${allPids.size} processes to cleanup: ${Array.from(allPids).join(', ')}`);
  
  // First try graceful termination
  const gracefulPromises = Array.from(allPids).map(async (pid) => {
    const killed = await killProcess(pid, 'TERM');
    if (killed) {
      const exited = await waitForProcessToExit(pid);
      if (!exited) {
        console.log(`⚠️  Process ${pid} didn't exit gracefully, will force kill`);
        return pid;
      }
    }
    return null;
  });
  
  const stubborn = (await Promise.all(gracefulPromises)).filter(pid => pid !== null);
  
  // Force kill stubborn processes
  if (stubborn.length > 0) {
    console.log(`💀 Force killing ${stubborn.length} stubborn processes...`);
    await Promise.all(stubborn.map(pid => killProcess(pid, 'KILL')));
  }
  
  console.log('🎉 Cleanup completed!');
}

// Handle script termination
process.on('SIGINT', () => {
  console.log('\n🛑 Cleanup interrupted');
  process.exit(1);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Cleanup terminated');
  process.exit(1);
});

// Run cleanup
cleanup().catch((error) => {
  console.error('❌ Cleanup failed:', error);
  process.exit(1);
});
