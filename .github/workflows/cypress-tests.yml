name: Cypress Regression Tests

on:
  push:
    branches: [master]
  pull_request:
    branches: [master]

jobs:
  regression-tests:
    runs-on: ubuntu-latest
    timeout-minutes: 20

    steps:
      # 1. Checkout repository
      - name: Checkout code
        uses: actions/checkout@v4

      # 2. Setup Node.js
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      # 3. Cache dependencies (root + FE + BE)
      - name: Cache root node_modules
        uses: actions/cache@v3
        with:
          path: node_modules
          key: ${{ runner.os }}-root-${{ hashFiles('package-lock.json') }}

      - name: Cache frontend node_modules
        uses: actions/cache@v3
        with:
          path: Todo App/frontend/node_modules
          key: ${{ runner.os }}-frontend-${{ hashFiles('Todo App/frontend/package-lock.json') }}

      - name: Cache backend node_modules
        uses: actions/cache@v3
        with:
          path: Todo App/backend/node_modules
          key: ${{ runner.os }}-backend-${{ hashFiles('Todo App/backend/package-lock.json') }}

      # 4. Install all dependencies (root + FE + BE)
      - name: Install all dependencies
        run: npm run install:all

      # 5. Start full stack server (FE + BE concurrently)
      - name: Start server
        run: npm run start &

      # 6. Wait for server to be ready
      - name: Wait for server
        run: sleep 45

      # 7. Run Cypress regression tests (tagged @Regression)
      - name: Run Cypress Regression Tests
        run: npm run e2e:regression:tests:ci
        env:
          CI: true

      # 8. Upload Mochawesome HTML report
      - name: Upload HTML report
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: Cypress HTML Report
          path: Automation Framework/cypress/reports/html

      # 9. Upload screenshots ONLY on failure
      - name: Upload screenshots
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: Cypress Screenshots
          path: Automation Framework/cypress/screenshots

      # 10. Upload videos ONLY on failure
      - name: Upload videos
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: Cypress Videos
          path: Automation Framework/cypress/videos
