name: 🚀 Cypress E2E Tests

on:
  push:
    branches: [master]
  pull_request:
    branches: [master]
  workflow_dispatch: # Allow manual triggering

# Optimize concurrency - cancel previous runs on new pushes
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  # Global environment variables for optimization
  NODE_ENV: test
  CI: true
  CYPRESS_CACHE_FOLDER: ~/.cache/Cypress
  # Disable unnecessary features for speed
  HUSKY: 0
  CYPRESS_RECORD_KEY: false

jobs:
  # Single job for E2E tests (simplified)
  e2e-tests:
    name: 🧪 Cypress E2E Tests
    runs-on: ubuntu-latest
    timeout-minutes: 12

    steps:
      - name: ⚡ Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: ⚡ Setup Node.js with caching
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: |
            package-lock.json
            Todo App/frontend/package-lock.json
            Todo App/backend/package-lock.json

      # Unified caching strategy
      - name: 🗄️ Cache dependencies & Cypress
        uses: actions/cache@v4
        with:
          path: |
            node_modules
            Todo App/frontend/node_modules
            Todo App/backend/node_modules
            ~/.cache/Cypress
            ~/.npm
          key: ${{ runner.os }}-all-deps-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-all-deps-

      # Single command to install, start, wait, and test
      - name: 🚀 Run complete CI pipeline
        run: npm run ci:regression
       

      # Kill any remaining processes
      - name: 🧹 Cleanup processes
        if: always()
        run: |
          pkill -f "node.*Todo App" || true
          pkill -f "react-scripts" || true

      # Streamlined artifact uploads
      - name: 📊 Upload test reports
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: cypress-reports-${{ github.run_number }}
          path: Automation Framework/cypress/reports/
          retention-days: 7
          compression-level: 6

      - name: 📸 Upload failure artifacts
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: cypress-failures-${{ github.run_number }}
          path: |
            Automation Framework/cypress/screenshots/
            Automation Framework/cypress/videos/
          retention-days: 3
          compression-level: 9

      # Test results summary
      - name: 📋 Test Results Summary
        if: always()
        run: |
          echo "## 🧪 Cypress Test Results" >> $GITHUB_STEP_SUMMARY
          echo "| Metric | Value |" >> $GITHUB_STEP_SUMMARY
          echo "|--------|-------|" >> $GITHUB_STEP_SUMMARY
          echo "| Test Suite | Regression |" >> $GITHUB_STEP_SUMMARY
          echo "| Node Version | 18 |" >> $GITHUB_STEP_SUMMARY
          echo "| Browser | Chrome (Headless) |" >> $GITHUB_STEP_SUMMARY
          echo "| Timestamp | $(date -u) |" >> $GITHUB_STEP_SUMMARY

          if [ -f "Automation Framework/cypress/reports/html/index.html" ]; then
            echo "| Report Status | ✅ Generated |" >> $GITHUB_STEP_SUMMARY
          else
            echo "| Report Status | ❌ Failed |" >> $GITHUB_STEP_SUMMARY
          fi

          # Add job status
          if [ "${{ job.status }}" = "success" ]; then
            echo "| Overall Status | ✅ Passed |" >> $GITHUB_STEP_SUMMARY
          else
            echo "| Overall Status | ❌ Failed |" >> $GITHUB_STEP_SUMMARY
          fi
