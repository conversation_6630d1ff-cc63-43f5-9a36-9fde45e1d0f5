name: 🚀 Cypress E2E Tests

on:
  push:
    branches: [master]
  pull_request:
    branches: [master]
  workflow_dispatch: # Allow manual triggering

# Optimize concurrency - cancel previous runs on new pushes
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  # Global environment variables for optimization
  NODE_ENV: test
  CI: true
  CYPRESS_CACHE_FOLDER: ~/.cache/Cypress
  # Disable unnecessary features for speed
  HUSKY: 0
  CYPRESS_RECORD_KEY: false

jobs:
  # Job 1: Quick validation (fast feedback)
  validate:
    name: 🔍 Validate & Setup
    runs-on: ubuntu-latest
    timeout-minutes: 5
    outputs:
      cache-hit: ${{ steps.cache-deps.outputs.cache-hit }}

    steps:
      - name: ⚡ Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: ⚡ Setup Node.js with caching
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: |
            package-lock.json
            Todo App/frontend/package-lock.json
            Todo App/backend/package-lock.json

      # Unified dependency caching
      - name: 🗄️ Cache dependencies
        id: cache-deps
        uses: actions/cache@v4
        with:
          path: |
            node_modules
            Todo App/frontend/node_modules
            Todo App/backend/node_modules
            ~/.cache/Cypress
            ~/.npm
          key: ${{ runner.os }}-deps-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-deps-

      - name: 📦 Install dependencies (using custom script)
        if: steps.cache-deps.outputs.cache-hit != 'true'
        run: npm run install:ci

      - name: 🔧 Verify Cypress
        run: npm run cy:verify

  # Job 2: E2E Tests (depends on validate)
  e2e-tests:
    name: 🧪 E2E Tests
    runs-on: ubuntu-latest
    timeout-minutes: 12
    needs: validate
    strategy:
      fail-fast: false
      matrix:
        test-suite: [regression]

    steps:
      - name: ⚡ Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: ⚡ Setup Node.js with caching
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: |
            package-lock.json
            Todo App/frontend/package-lock.json
            Todo App/backend/package-lock.json

      # Restore cached dependencies
      - name: 🗄️ Restore dependencies cache
        uses: actions/cache@v4
        with:
          path: |
            node_modules
            Todo App/frontend/node_modules
            Todo App/backend/node_modules
            ~/.cache/Cypress
            ~/.npm
          key: ${{ runner.os }}-deps-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-deps-

      # Fallback install if cache miss
      - name: 📦 Install dependencies (fallback)
        if: needs.validate.outputs.cache-hit != 'true'
        run: npm run install:ci

      # Start services and run tests (using custom scripts)
      - name: 🚀 Start services & run tests
        run: npm run ci:regression
        env:
          CYPRESS_BASE_URL: http://localhost:3000
          CYPRESS_API_BASE_URL: http://localhost:3001/api
          CYPRESS_VIDEO: false
          CYPRESS_SCREENSHOTS: true
          CYPRESS_CACHE_FOLDER: ~/.cache/Cypress
          CYPRESS_CHROME_FLAGS: --no-sandbox --disable-dev-shm-usage

      # Cleanup is handled by the custom script

      # Streamlined artifact uploads
      - name: 📊 Upload test reports
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: cypress-reports-${{ github.run_number }}
          path: Automation Framework/cypress/reports/
          retention-days: 7
          compression-level: 6

      - name: 📸 Upload failure artifacts
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: cypress-failures-${{ github.run_number }}
          path: |
            Automation Framework/cypress/screenshots/
            Automation Framework/cypress/videos/
          retention-days: 3
          compression-level: 9

      # Enhanced test results summary
      - name: 📋 Test Results Summary
        if: always()
        run: |
          echo "## 🧪 Cypress Test Results" >> $GITHUB_STEP_SUMMARY
          echo "| Metric | Value |" >> $GITHUB_STEP_SUMMARY
          echo "|--------|-------|" >> $GITHUB_STEP_SUMMARY
          echo "| Test Suite | ${{ matrix.test-suite }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Node Version | 18 |" >> $GITHUB_STEP_SUMMARY
          echo "| Browser | Chrome (Headless) |" >> $GITHUB_STEP_SUMMARY
          echo "| Timestamp | $(date -u) |" >> $GITHUB_STEP_SUMMARY
          echo "| Cache Hit | ${{ needs.validate.outputs.cache-hit }} |" >> $GITHUB_STEP_SUMMARY

          if [ -f "Automation Framework/cypress/reports/html/index.html" ]; then
            echo "| Report Status | ✅ Generated |" >> $GITHUB_STEP_SUMMARY
          else
            echo "| Report Status | ❌ Failed |" >> $GITHUB_STEP_SUMMARY
          fi

          # Add job status
          if [ "${{ job.status }}" = "success" ]; then
            echo "| Overall Status | ✅ Passed |" >> $GITHUB_STEP_SUMMARY
          else
            echo "| Overall Status | ❌ Failed |" >> $GITHUB_STEP_SUMMARY
          fi
