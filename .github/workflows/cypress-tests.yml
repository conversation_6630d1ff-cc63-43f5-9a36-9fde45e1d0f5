name: 🚀 Cypress Regression Tests

on:
  push:
    branches: [master]
  pull_request:
    branches: [master]
  workflow_dispatch:

# Cancel previous runs on the same branch
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  NODE_ENV: test
  CI: true
  CYPRESS_CACHE_FOLDER: ~/.cache/Cypress
  HUSKY: 0
  CYPRESS_RECORD_KEY: false

jobs:
  regression-tests:
    name: 🧪 Cypress E2E Tests
    runs-on: ubuntu-latest
    timeout-minutes: 15

    steps:
      # Checkout repository
      - name: ⚡ Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 1

      # Setup Node.js with caching
      - name: ⚡ Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: |
            package-lock.json
            Todo App/frontend/package-lock.json
            Todo App/backend/package-lock.json

      # Cache Cypress + node_modules
      - name: 🗄️ Cache dependencies
        uses: actions/cache@v4
        with:
          path: |
            node_modules
            Todo App/frontend/node_modules
            Todo App/backend/node_modules
            ~/.cache/Cypress
            ~/.npm
          key: ${{ runner.os }}-deps-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-deps-

      # Run CI Regression Flow (install + start services + tests)
      - name: 🧪 Run Cypress Regression Tests
        run: npm run ci:regression
        env:
          CYPRESS_VIDEO: false
          CYPRESS_SCREENSHOTS: true
          CYPRESS_CHROME_FLAGS: --no-sandbox --disable-dev-shm-usage

      # Merge Mochawesome JSON into single HTML report
      - name: 🧩 Merge Cypress Reports
        if: always()
        run: |
          npx mochawesome-merge 'Automation Framework/cypress/reports/json/*.json' > 'Automation Framework/cypress/reports/mochawesome.json'
          npx marge 'Automation Framework/cypress/reports/mochawesome.json' -f report -o 'Automation Framework/cypress/reports/html'

      # Upload combined report (always)
      - name: 📊 Upload Cypress Report
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: cypress-report-${{ github.run_number }}
          path: Automation Framework/cypress/reports/html
          retention-days: 7
          compression-level: 6

      # Upload failure artifacts (screenshots/videos only on failure)
      - name: 📸 Upload Failure Artifacts
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: cypress-failures-${{ github.run_number }}
          path: |
            Automation Framework/cypress/screenshots
            Automation Framework/cypress/videos
          retention-days: 3
          compression-level: 9

      # Cleanup processes (avoid port conflicts)
      - name: 🧹 Cleanup processes
        if: always()
        run: |
          pkill -f "node.*Todo App" || true
          pkill -f "react-scripts" || true

      # Add summary to GitHub UI
      - name: 📋 Test Results Summary
        if: always()
        run: |
          echo "## 🧪 Cypress Test Results" >> $GITHUB_STEP_SUMMARY
          echo "| Metric | Value |" >> $GITHUB_STEP_SUMMARY
          echo "|--------|-------|" >> $GITHUB_STEP_SUMMARY
          echo "| Test Suite | Regression |" >> $GITHUB_STEP_SUMMARY
          echo "| Node Version | 18 |" >> $GITHUB_STEP_SUMMARY
          echo "| Browser | Chrome (Headless) |" >> $GITHUB_STEP_SUMMARY
          echo "| Timestamp | $(date -u) |" >> $GITHUB_STEP_SUMMARY

          if [ -f "Automation Framework/cypress/reports/html/index.html" ]; then
            echo "| Report Status | ✅ Generated |" >> $GITHUB_STEP_SUMMARY
          else
            echo "| Report Status | ❌ Failed |" >> $GITHUB_STEP_SUMMARY
          fi

          if [ "${{ job.status }}" = "success" ]; then
            echo "| Overall Status | ✅ Passed |" >> $GITHUB_STEP_SUMMARY
          else
            echo "| Overall Status | ❌ Failed |" >> $GITHUB_STEP_SUMMARY
          fi
