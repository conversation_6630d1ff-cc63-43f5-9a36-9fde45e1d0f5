name: 🚀 Cypress E2E Tests

on:
  push:
    branches: [master, main, develop]
  pull_request:
    branches: [master, main, develop]
  workflow_dispatch: # Allow manual triggering

# Optimize concurrency - cancel previous runs on new pushes
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  # Global environment variables for optimization
  NODE_ENV: test
  CI: true
  CYPRESS_CACHE_FOLDER: ~/.cache/Cypress
  # Disable unnecessary features for speed
  HUSKY: 0
  CYPRESS_RECORD_KEY: false

jobs:
  # Job 1: Lint and validate (fast feedback)
  validate:
    name: 🔍 Validate Code
    runs-on: ubuntu-latest
    timeout-minutes: 5
    steps:
      - name: ⚡ Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 1 # Shallow clone for speed

      - name: ⚡ Setup Node.js with caching
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: |
            package-lock.json
            Todo App/frontend/package-lock.json
            Todo App/backend/package-lock.json

      - name: 📦 Install dependencies (production only)
        run: |
          npm ci --only=production --silent
          cd "Todo App/backend" && npm ci --only=production --silent
          cd "../frontend" && npm ci --only=production --silent

  # Job 2: Build and test (parallel with validate)
  e2e-tests:
    name: 🧪 E2E Tests
    runs-on: ubuntu-latest
    timeout-minutes: 15
    strategy:
      fail-fast: false
      matrix:
        # Split tests for parallel execution if needed
        test-suite: [regression]

    steps:
      - name: ⚡ Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: ⚡ Setup Node.js with caching
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: |
            package-lock.json
            Todo App/frontend/package-lock.json
            Todo App/backend/package-lock.json

      # Advanced caching strategy
      - name: 🗄️ Cache Cypress binary
        uses: actions/cache@v4
        with:
          path: ~/.cache/Cypress
          key: cypress-${{ runner.os }}-${{ hashFiles('package-lock.json') }}
          restore-keys: |
            cypress-${{ runner.os }}-

      - name: 🗄️ Cache node_modules (unified)
        uses: actions/cache@v4
        with:
          path: |
            node_modules
            Todo App/frontend/node_modules
            Todo App/backend/node_modules
            ~/.npm
          key: ${{ runner.os }}-node-modules-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-modules-

      # Optimized dependency installation
      - name: 📦 Install dependencies (optimized)
        run: |
          # Use npm ci for faster, reliable installs
          npm ci --silent --prefer-offline
          cd "Todo App/backend" && npm ci --silent --prefer-offline
          cd "../frontend" && npm ci --silent --prefer-offline

      - name: 🔧 Verify Cypress installation
        run: npx cypress verify

      # Health check based server startup (replaces sleep)
      - name: 🚀 Start application servers
        run: |
          # Start backend in background
          cd "Todo App/backend" && npm start &
          BACKEND_PID=$!
          echo "BACKEND_PID=$BACKEND_PID" >> $GITHUB_ENV

          # Start frontend in background
          cd "Todo App/frontend" && npm start &
          FRONTEND_PID=$!
          echo "FRONTEND_PID=$FRONTEND_PID" >> $GITHUB_ENV

      - name: ⏳ Wait for services (health check)
        run: |
          # Function to check service health
          check_service() {
            local url=$1
            local service_name=$2
            local max_attempts=30
            local attempt=1

            echo "🔍 Checking $service_name health..."
            while [ $attempt -le $max_attempts ]; do
              if curl -f -s "$url" > /dev/null 2>&1; then
                echo "✅ $service_name is ready!"
                return 0
              fi
              echo "⏳ Attempt $attempt/$max_attempts - $service_name not ready yet..."
              sleep 2
              attempt=$((attempt + 1))
            done
            echo "❌ $service_name failed to start within timeout"
            return 1
          }

          # Check backend health endpoint
          check_service "http://localhost:3001/health" "Backend API"

          # Check frontend
          check_service "http://localhost:3000" "Frontend App"

          echo "🎉 All services are ready!"

      # Optimized Cypress execution
      - name: 🧪 Run Cypress Tests
        run: |
          cd "Automation Framework"
          npx cypress run \
            --env grepTags=@Regression,grepFilterSpecs=true \
            --browser chrome \
            --headless \
            --config video=false,screenshotOnRunFailure=true \
            --reporter cypress-mochawesome-reporter \
            --reporter-options reportDir=cypress/reports,overwrite=false,html=true,json=true
        env:
          # Optimize Cypress performance
          CYPRESS_BASE_URL: http://localhost:3000
          CYPRESS_API_BASE_URL: http://localhost:3001/api
          CYPRESS_VIDEO: false
          CYPRESS_SCREENSHOTS: true
          CYPRESS_CACHE_FOLDER: ~/.cache/Cypress
          # Disable Chrome sandbox for CI
          CYPRESS_CHROME_FLAGS: --no-sandbox --disable-dev-shm-usage

      # Cleanup processes
      - name: 🧹 Cleanup processes
        if: always()
        run: |
          if [ ! -z "$BACKEND_PID" ]; then
            kill $BACKEND_PID 2>/dev/null || true
          fi
          if [ ! -z "$FRONTEND_PID" ]; then
            kill $FRONTEND_PID 2>/dev/null || true
          fi

      # Optimized artifact uploads
      - name: 📊 Upload test reports
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: cypress-reports-${{ matrix.test-suite }}-${{ github.run_number }}
          path: Automation Framework/cypress/reports/
          retention-days: 7
          compression-level: 6

      - name: 📸 Upload failure screenshots
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: cypress-screenshots-${{ matrix.test-suite }}-${{ github.run_number }}
          path: Automation Framework/cypress/screenshots/
          retention-days: 3
          compression-level: 9

      # Test results summary
      - name: 📋 Test Results Summary
        if: always()
        run: |
          echo "## 🧪 Test Results Summary" >> $GITHUB_STEP_SUMMARY
          echo "- **Test Suite**: ${{ matrix.test-suite }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Node Version**: 18" >> $GITHUB_STEP_SUMMARY
          echo "- **Browser**: Chrome (Headless)" >> $GITHUB_STEP_SUMMARY
          echo "- **Timestamp**: $(date -u)" >> $GITHUB_STEP_SUMMARY

          if [ -f "Automation Framework/cypress/reports/html/index.html" ]; then
            echo "- **Report**: Available in artifacts" >> $GITHUB_STEP_SUMMARY
          fi
