{"name": "cypress-todo-app-assignment", "version": "1.0.0", "description": "A full-stack Todo application with React frontend, Node.js backend, and comprehensive Cypress testing framework", "main": "index.js", "workspaces": ["Todo App/frontend", "Todo App/backend"], "scripts": {"cy:open": "cypress open --project 'Automation Framework'", "cy:run": "cypress run --project 'Automation Framework'", "cy:verify": "cypress verify --project 'Automation Framework'", "install:all": "npm ci --prefer-offline --no-audit && cd 'Todo App/frontend' && npm ci --prefer-offline --no-audit && cd ../backend && npm ci --prefer-offline --no-audit", "test:regression": "npm run cy:run -- --env grepTags=@Regression,grepFilterSpecs=true", "test:regression:report": "npm run test:regression && npx mochawesome-merge 'Automation Framework/cypress/reports/json/*.json' > 'Automation Framework/cypress/reports/mochawesome.json' && npx marge 'Automation Framework/cypress/reports/mochawesome.json' -f report -o 'Automation Framework/cypress/reports/html' && open 'Automation Framework/cypress/reports/html/index.html'", "ci:regression": "npm run install:all && npm run start & sleep 30 && npm run cy:run -- --env grepTags=@Regression,grepFilterSpecs=true --browser chrome --headless --config video=false,screenshotOnRunFailure=true --reporter cypress-mochawesome-reporter --reporter-options reportDir=cypress/reports,overwrite=false,html=true,json=true"}, "repository": {"type": "git", "url": "git+https://github.com/aashir1998/Cypress-Todo-App-Assignment.git"}, "keywords": ["cypress", "testing", "automation", "e2e", "api", "todo-app", "react", "nodejs", "express"], "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "type": "commonjs", "bugs": {"url": "https://github.com/aashir1998/Cypress-Todo-App-Assignment/issues"}, "homepage": "https://github.com/aashir1998/Cypress-Todo-App-Assignment#readme", "devDependencies": {"@cypress/grep": "^4.1.0", "@eslint/js": "^9.20.0", "@faker-js/faker": "^9.9.0", "concurrently": "^9.2.0", "cypress": "^14.5.3", "cypress-mochawesome-reporter": "^3.8.2", "cypress-plugin-api": "^2.11.2", "cypress-recurse": "^1.35.3", "cypress-terminal-report": "^7.2.1", "cypress-visual-regression": "^5.3.0", "dotenv": "^16.4.7", "eslint": "^9.20.1", "eslint-config-prettier": "^10.0.1", "eslint-plugin-cypress": "^4.1.0", "globals": "^15.15.0", "import": "^0.0.6", "prettier": "3.5.1"}}