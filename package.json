{"name": "cypress-todo-app-assignment", "version": "1.0.0", "description": "A full-stack Todo application with React frontend, Node.js backend, and comprehensive Cypress testing framework", "main": "index.js", "workspaces": ["Todo App/frontend", "Todo App/backend"], "scripts": {"cy:verify": "cd \"Automation Framework\" && cypress verify", "cy:run": "cd \"Automation Framework\" && cypress run", "cy:open": "cd \"Automation Framework\" && cypress open", "e2e:smoke:tests": "cd \"Automation Framework\" && cypress run --env grepTags=@Smoke,grepFilterSpecs=true", "e2e:regression:tests": "cd \"Automation Framework\" && cypress run --env grepTags=@Regression,grepFilterSpecs=true", "e2e:regression:tests:ci": "cd \"Automation Framework\" && cypress run --env grepTags=@Regression,grepFilterSpecs=true --browser chrome --headless --config video=false,screenshotOnRunFailure=true --reporter cypress-mochawesome-reporter --reporter-options reportDir=cypress/reports,overwrite=false,html=true,json=true", "test:all": "cd \"Automation Framework\" && cypress run", "start:backend": "cd \"Todo App/backend\" && npm start", "start:frontend": "cd \"Todo App/frontend\" && npm start", "dev": "concurrently \"npm run start:backend\" \"npm run start:frontend\"", "start": "npm run dev", "install:all": "npm install && npm run install:backend && npm run install:frontend", "install:backend": "cd \"Todo App/backend\" && npm install", "install:frontend": "cd \"Todo App/frontend\" && npm install", "install:ci": "npm ci --silent --prefer-offline && cd \"Todo App/backend\" && npm ci --silent --prefer-offline && cd \"../frontend\" && npm ci --silent --prefer-offline", "health:check": "curl -f -s http://localhost:3001/health && curl -f -s http://localhost:3000", "wait:services": "timeout 60 bash -c 'until npm run health:check; do echo \"⏳ Waiting for services...\"; sleep 2; done' && echo \"✅ All services ready!\"", "start:ci": "npm run start &", "ci:regression": "npm run install:ci && npm run start:ci && npm run wait:services && npm run e2e:regression:tests:ci", "generate:report:local": "npm run e2e:regression:tests && echo '✅ Report generated at: Automation Framework/cypress/reports/html/index.html' && open 'Automation Framework/cypress/reports/html/index.html'"}, "repository": {"type": "git", "url": "git+https://github.com/aashir1998/Cypress-Todo-App-Assignment.git"}, "keywords": ["cypress", "testing", "automation", "e2e", "api", "todo-app", "react", "nodejs", "express"], "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "type": "commonjs", "bugs": {"url": "https://github.com/aashir1998/Cypress-Todo-App-Assignment/issues"}, "homepage": "https://github.com/aashir1998/Cypress-Todo-App-Assignment#readme", "devDependencies": {"@cypress/grep": "^4.1.0", "@eslint/js": "^9.20.0", "@faker-js/faker": "^9.9.0", "concurrently": "^9.2.0", "cypress": "^14.5.3", "cypress-mochawesome-reporter": "^3.8.2", "cypress-plugin-api": "^2.11.2", "cypress-recurse": "^1.35.3", "cypress-terminal-report": "^7.2.1", "cypress-visual-regression": "^5.3.0", "dotenv": "^16.4.7", "eslint": "^9.20.1", "eslint-config-prettier": "^10.0.1", "eslint-plugin-cypress": "^4.1.0", "globals": "^15.15.0", "import": "^0.0.6", "prettier": "3.5.1"}}